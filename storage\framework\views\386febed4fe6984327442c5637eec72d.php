<?php $__env->startSection('content'); ?>
<div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6 text-gray-900">
        <h1 class="text-3xl font-bold mb-8">Business Management Dashboard</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Companies Card -->
            <div class="bg-blue-50 p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Companies</h3>
                        <p class="text-sm text-gray-600"><?php echo e(\App\Models\Company::count()); ?> total</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="<?php echo e(route('companies.index')); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View all companies →
                    </a>
                </div>
            </div>

            <!-- Clients Card -->
            <div class="bg-green-50 p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Clients</h3>
                        <p class="text-sm text-gray-600"><?php echo e(\App\Models\Client::count()); ?> total</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="<?php echo e(route('clients.index')); ?>" class="text-green-600 hover:text-green-800 text-sm font-medium">
                        View all clients →
                    </a>
                </div>
            </div>

            <!-- Projects Card -->
            <div class="bg-yellow-50 p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Projects</h3>
                        <p class="text-sm text-gray-600"><?php echo e(\App\Models\Project::count()); ?> total</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="<?php echo e(route('projects.index')); ?>" class="text-yellow-600 hover:text-yellow-800 text-sm font-medium">
                        View all projects →
                    </a>
                </div>
            </div>

            <!-- Invoices Card -->
            <div class="bg-purple-50 p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Invoices</h3>
                        <p class="text-sm text-gray-600"><?php echo e(\App\Models\Invoice::count()); ?> total</p>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="<?php echo e(route('invoices.index')); ?>" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                        View all invoices →
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="<?php echo e(route('companies.create')); ?>" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-center transition duration-200">
                    Add Company
                </a>
                <a href="<?php echo e(route('clients.create')); ?>" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-center transition duration-200">
                    Add Client
                </a>
                <a href="<?php echo e(route('projects.create')); ?>" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-center transition duration-200">
                    Create Project
                </a>
                <a href="<?php echo e(route('invoices.create')); ?>" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-center transition duration-200">
                    Create Invoice
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\proj\fresh-business-system\resources\views/dashboard.blade.php ENDPATH**/ ?>