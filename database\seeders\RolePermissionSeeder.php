<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Company permissions
            'view companies',
            'create companies',
            'edit companies',
            'delete companies',

            // Client permissions
            'view clients',
            'create clients',
            'edit clients',
            'delete clients',

            // Service permissions
            'view services',
            'create services',
            'edit services',
            'delete services',

            // Project permissions
            'view projects',
            'create projects',
            'edit projects',
            'delete projects',

            // Invoice permissions
            'view invoices',
            'create invoices',
            'edit invoices',
            'delete invoices',
            'send invoices',

            // Quotation permissions
            'view quotations',
            'create quotations',
            'edit quotations',
            'delete quotations',
            'send quotations',

            // Payment permissions
            'view payments',
            'create payments',
            'edit payments',
            'delete payments',

            // User permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles
        $adminRole = Role::create(['name' => 'admin']);
        $managerRole = Role::create(['name' => 'manager']);
        $employeeRole = Role::create(['name' => 'employee']);
        $clientRole = Role::create(['name' => 'client']);

        // Assign permissions to roles
        $adminRole->givePermissionTo(Permission::all());

        $managerRole->givePermissionTo([
            'view companies', 'edit companies',
            'view clients', 'create clients', 'edit clients',
            'view services', 'create services', 'edit services',
            'view projects', 'create projects', 'edit projects',
            'view invoices', 'create invoices', 'edit invoices', 'send invoices',
            'view quotations', 'create quotations', 'edit quotations', 'send quotations',
            'view payments', 'create payments', 'edit payments',
            'view users', 'create users', 'edit users',
        ]);

        $employeeRole->givePermissionTo([
            'view companies',
            'view clients', 'create clients', 'edit clients',
            'view services',
            'view projects', 'create projects', 'edit projects',
            'view invoices', 'create invoices', 'edit invoices',
            'view quotations', 'create quotations', 'edit quotations',
            'view payments', 'create payments',
        ]);

        $clientRole->givePermissionTo([
            'view projects',
            'view invoices',
            'view quotations',
            'view payments',
        ]);
    }
}
