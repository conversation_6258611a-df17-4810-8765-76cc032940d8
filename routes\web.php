<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\QuotationController;
use App\Http\Controllers\PaymentController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Business management routes with permissions
    Route::resource('companies', CompanyController::class)->middleware('permission:view companies');
    Route::resource('clients', ClientController::class)->middleware('permission:view clients');
    Route::resource('services', ServiceController::class)->middleware('permission:view services');
    Route::resource('projects', ProjectController::class)->middleware('permission:view projects');
    Route::resource('invoices', InvoiceController::class)->middleware('permission:view invoices');
    Route::resource('quotations', QuotationController::class)->middleware('permission:view quotations');
    Route::resource('payments', PaymentController::class)->middleware('permission:view payments');
});

require __DIR__.'/auth.php';
