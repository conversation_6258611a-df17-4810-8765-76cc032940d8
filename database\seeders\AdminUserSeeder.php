<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Company;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a sample company
        $company = Company::create([
            'name' => 'Demo Company',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'website' => 'https://democompany.com',
            'address' => '123 Business Street',
            'city' => 'Business City',
            'state' => 'Business State',
            'postal_code' => '12345',
            'country' => 'United States',
            'tax_number' => 'TAX123456',
            'registration_number' => 'REG789012',
            'description' => 'A demo company for testing the business management system.',
            'is_active' => true,
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'company_id' => $company->id,
            'email_verified_at' => now(),
        ]);

        // Assign admin role
        $admin->assignRole('admin');

        // Create manager user
        $manager = User::create([
            'name' => 'Manager User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'company_id' => $company->id,
            'email_verified_at' => now(),
        ]);

        // Assign manager role
        $manager->assignRole('manager');

        // Create employee user
        $employee = User::create([
            'name' => 'Employee User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'company_id' => $company->id,
            'email_verified_at' => now(),
        ]);

        // Assign employee role
        $employee->assignRole('employee');
    }
}
